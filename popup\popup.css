* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #0077b5;
    --secondary-color: #0073b1;
    --text-color: #333;
    --light-gray: #f3f6f8;
    --border-color: #e0e0e0;
    --success-color: #0a8a0a;
    --warning-color: #f5a623;
    --error-color: #e74c3c;
}

body {
    width: 400px;
    height: 500px;
    overflow: hidden;
    background-color: #fff;
    color: var(--text-color);
}

.hidden {
    display: none !important;
}

/* Ensure flex elements show properly when not hidden */
.auto-detection-indicator:not(.hidden) {
    display: flex !important;
}

.no-messages-placeholder {
    padding: 20px;
    background: #f0f0f0;
    margin: 10px 0;
    border: 2px solid #007bff;
    border-radius: 4px;
    text-align: center;
    color: #666;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 18px;
    color: var(--primary-color);
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    background-color: var(--light-gray);
}

.status.running {
    background-color: var(--success-color);
    color: white;
}

.status.paused {
    background-color: var(--warning-color);
    color: white;
}

/* Simplified single tab interface */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 10px;
    background: none;
    border: none;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    cursor: default;
}

.tab-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    display: block;
}

.section {
    margin-bottom: 20px;
}

.section h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

input, textarea, select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

textarea {
    min-height: 60px;
    resize: vertical;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #888;
    font-style: italic;
}

.campaign-item {
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--light-gray);
}

.campaign-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.campaign-title {
    font-weight: 500;
}

.campaign-actions {
    display: flex;
    gap: 5px;
}

.campaign-stats {
    font-size: 12px;
    color: #666;
}

.campaign-messaging {
    font-size: 11px;
    color: #0073b1;
    margin-top: 4px;
    padding: 4px 8px;
    background: #e3f2fd;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

footer {
    padding: 10px 15px;
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: #666;
}

.stats {
    display: flex;
    justify-content: space-between;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 450px;
    max-height: 90vh;
    overflow: hidden;
}

/* Campaign Wizard Styles */
.campaign-wizard {
    padding: 0;
}

.wizard-step {
    display: none;
    padding: 0;
}

.wizard-step.active {
    display: block;
}

.wizard-header {
    background-color: var(--primary-color);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wizard-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
}

.step-indicator {
    background-color: #f8f9fa;
    padding: 10px 20px;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #e0e0e0;
}

.wizard-step .form-group {
    padding: 20px;
}

.campaign-input {
    width: 100%;
    padding: 15px;
    border: none;
    border-bottom: 2px solid var(--primary-color);
    font-size: 16px;
    outline: none;
    background: transparent;
}

.wizard-actions {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #e0e0e0;
}

.wizard-next {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

/* Add People Options */
.add-people-options {
    padding: 20px;
}

.option-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.option-btn.disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.option-btn:hover:not(.disabled) {
    background-color: var(--secondary-color);
}

.or-divider {
    text-align: center;
    margin: 20px 0;
    color: #666;
    position: relative;
}

.or-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #e0e0e0;
    z-index: 1;
}

.or-divider {
    background-color: white;
    padding: 0 10px;
    z-index: 2;
    position: relative;
}

.csv-upload-area {
    border: 2px dashed #ccc;
    padding: 30px;
    text-align: center;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.upload-text {
    margin-bottom: 15px;
    color: #666;
}

.upload-btn {
    color: var(--primary-color);
    background: none;
    border: none;
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
}

.upload-hint {
    margin-top: 10px;
    font-size: 12px;
    color: #999;
}

/* Search Instructions */
.search-instructions {
    padding: 20px;
    background-color: #f8f9fa;
    margin: 0;
}

.search-instructions p {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.search-actions {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Collection Status */
.collection-status {
    padding: 20px;
}

.auto-detection-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    animation: pulse 2s infinite;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.collected-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.collection-status-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.collection-controls {
    margin: 10px 0;
}

.auto-collection-info {
    margin-top: 10px;
    padding: 8px;
    background-color: #e8f4fd;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

.auto-collection-info small {
    color: #666;
    line-height: 1.4;
}

.collected-profiles {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

#next-to-messaging {
    width: 100%;
    margin-top: 10px;
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.profile-card {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.profile-details {
    font-size: 12px;
    color: #666;
}

/* Duplicates Modal */
.duplicates-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
}

.duplicates-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
}

.duplicates-content h3 {
    margin-bottom: 15px;
    color: #333;
}

.duplicates-content p {
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
}

.duplicate-profiles {
    max-height: 200px;
    overflow-y: auto;
    margin: 20px 0;
}

.duplicate-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 20px;
    cursor: pointer;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Profile collection styles */
.profile-item {
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--light-gray);
}

.profile-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.profile-details {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.profile-url {
    font-size: 10px;
    color: #888;
    word-break: break-all;
}

/* Form enhancements */
.form-group small {
    display: block;
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* Quick actions section */
.section h3 {
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

/* Messaging Strategy Styles */
.messaging-strategy {
    padding: 20px;
}

.strategy-options {
    margin-bottom: 20px;
}

.strategy-option {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.strategy-option:hover {
    border-color: var(--primary-color);
    background-color: #f8f9ff;
}

.strategy-option input[type="radio"] {
    margin-right: 10px;
}

.strategy-option label {
    cursor: pointer;
    display: block;
    margin: 0;
}

.strategy-title {
    font-weight: bold;
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 5px;
}

.strategy-description {
    color: #666;
    font-size: 14px;
}

.strategy-option input[type="radio"]:checked + label .strategy-option,
.strategy-option:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background-color: #f8f9ff;
}

.follow-up-config {
    background-color: #f8f9ff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.follow-up-config h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.ai-analysis-config {
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.ai-analysis-config h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.ai-analysis-config .form-group {
    padding: 5px 0;
}

.ai-analysis-config label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.ai-analysis-config input[type="checkbox"] {
    margin-right: 8px;
}

/* Network Search Styles */
.network-search-options {
    padding: 20px;
}

.search-option {
    background-color: #f8f9ff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.search-option h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.search-option p {
    color: #666;
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.network-instructions {
    background-color: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.network-instructions h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.network-instructions ul {
    margin: 0;
    padding-left: 20px;
}

.network-instructions li {
    margin-bottom: 5px;
    color: #555;
}

.network-list-option {
    background-color: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    text-align: center;
}

.network-list-option h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.network-list-option p {
    color: #666;
    margin-bottom: 15px;
}

/* Profile URLs Modal Specific Styles */
#profile-urls-modal .modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
}

#profile-urls-modal .profile-urls-container {
    max-height: 500px;
    overflow-y: auto;
}

#profile-urls-modal .collection-status {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

#profile-urls-modal .collection-status p {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

#profile-urls-modal .collection-status .btn {
    margin: 0 8px;
    border: 2px solid white;
    background: transparent;
    color: white;
    font-weight: 600;
}

#profile-urls-modal .collection-status .btn:hover {
    background: white;
    color: #667eea;
}

#profile-urls-modal .profile-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    gap: 15px;
}

#profile-urls-modal .profile-pic {
    flex-shrink: 0;
}

#profile-urls-modal .profile-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

#profile-urls-modal .profile-checkbox {
    margin-right: 15px;
    transform: scale(1.2);
    cursor: pointer;
}

#profile-urls-modal .profile-info {
    flex: 1;
}

#profile-urls-modal .profile-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
    font-size: 14px;
}

#profile-urls-modal .profile-details {
    font-size: 12px;
    color: #666;
    margin-bottom: 6px;
}

#profile-urls-modal .profile-url {
    font-size: 11px;
    color: #0073b1;
    word-break: break-all;
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
}

/* Message Generation Styles */
.message-generation-section {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: #f8f9fa;
}

.message-generation-section h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 16px;
}

.generation-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
}

.generation-status {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: #e3f2fd;
    border-radius: 6px;
    margin: 10px 0;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generated-messages {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.generated-messages h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 14px;
}

.message-item {
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: #fafafa;
}

.message-profile {
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
    font-size: 13px;
}

.message-content {
    font-size: 12px;
    color: #555;
    line-height: 1.4;
    background: white;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

.message-status {
    margin-top: 6px;
    font-size: 11px;
}

.message-status.success {
    color: var(--success-color);
}

.message-status.error {
    color: var(--error-color);
}

.generation-summary {
    margin-top: 15px;
    padding: 10px;
    background: #e8f5e8;
    border-radius: 6px;
    font-size: 13px;
    color: #2e7d32;
}

/* Step 4: Profile Selection & Message Generation */
.profile-selection-section {
    padding: 15px 0;
}

.selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.selection-header h4 {
    margin: 0;
    color: var(--text-color);
}

.selection-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.btn-small:hover {
    background: var(--light-gray);
}

.selected-count {
    font-size: 12px;
    color: var(--text-color);
    font-weight: 500;
}

.profiles-selection-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 15px;
}

.profile-selection-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
}

.profile-selection-item:last-child {
    border-bottom: none;
}

.profile-selection-item:hover {
    background: var(--light-gray);
}

.profile-selection-item input[type="checkbox"] {
    margin-right: 10px;
}

.profile-selection-info {
    flex: 1;
}

.profile-selection-name {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2px;
}

.profile-selection-url {
    font-size: 11px;
    color: #666;
    text-decoration: none;
}

.generation-controls {
    text-align: center;
    margin-bottom: 20px;
}

.message-results {
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.messages-container {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.message-item {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 10px;
    overflow: hidden;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.message-profile-name {
    font-weight: 500;
    color: var(--text-color);
}

.message-select-checkbox {
    margin: 0;
}

.message-content {
    padding: 10px;
    background: white;
}

.message-text {
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-color);
    margin-bottom: 8px;
}

.message-meta {
    font-size: 11px;
    color: #666;
    display: flex;
    justify-content: space-between;
}

.message-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

/* Message Selection Styles */
.message-selection-step {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: white;
    z-index: 1000;
    overflow-y: auto;
    padding: 0;
}

.step-header {
    background: #f8f9fa;
    padding: 20px 30px;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.step-header h3 {
    margin: 10px 0 5px 0;
    font-size: 24px;
    color: var(--text-color);
}

.step-header p {
    margin: 0;
    color: #666;
    font-size: 16px;
}

.back-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 15px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s ease;
}

.back-btn:hover {
    background: #0056b3;
    text-decoration: none;
}

#message-selection-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px;
}

.profile-message-card {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    margin-bottom: 30px;
    padding: 25px;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.profile-message-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e8f4f8;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 8px;
    margin: -25px -25px 25px -25px;
}

.profile-info h4 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 20px;
    font-weight: 600;
}

.profile-title {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 15px;
    font-weight: 500;
}

.profile-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    padding: 4px 8px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
    transition: all 0.2s ease;
}

.profile-link:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.message-options h5 {
    margin: 0 0 20px 0;
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
}

.message-option {
    margin-bottom: 20px;
}

.message-label {
    display: block;
    cursor: pointer;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #fafafa;
    transition: all 0.3s ease;
    position: relative;
}

.message-label:hover {
    border-color: var(--primary-color);
    background: #f8f9ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.message-option input[type="radio"]:checked + .message-label {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f7ff 100%);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
    transform: translateY(-2px);
}

.message-option input[type="radio"]:checked + .message-label::before {
    content: "✓";
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.message-text {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    padding-right: 40px;
}

.error-section {
    text-align: center;
    padding: 20px;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
}

.error-text {
    color: #e53e3e;
    margin: 0 0 10px 0;
}

.btn-retry {
    background: #e53e3e;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-retry:hover {
    background: #c53030;
}

.selection-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin: 40px auto 0;
    padding: 30px;
    border-top: 2px solid var(--border-color);
    background: #f8f9fa;
    position: sticky;
    bottom: 0;
    max-width: 1200px;
}

.selection-actions .btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    min-width: 200px;
    transition: all 0.3s ease;
}

.selection-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.campaign-success {
    text-align: center;
    padding: 40px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.success-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.campaign-success h2 {
    color: var(--success-color);
    margin-bottom: 15px;
}

.campaign-summary {
    margin: 30px 0;
    text-align: left;
    max-height: 300px;
    overflow-y: auto;
}

.summary-item {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background: #fafafa;
}

.summary-item strong {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
}

.message-preview {
    font-size: 13px;
    color: #666;
    font-style: italic;
    margin: 0;
}

.final-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.campaign-creation {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

/* Profile Messages Section */
.profile-messages-section {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
    background: white;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.profile-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
}

.message-count {
    font-size: 12px;
    color: #666;
    background: white;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.error-badge {
    background: var(--error-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.error-message {
    padding: 15px;
    color: var(--error-color);
    background: #ffeaea;
    font-size: 13px;
}

.messages-list {
    padding: 15px;
}

.individual-message {
    margin-bottom: 12px;
}

.individual-message:last-child {
    margin-bottom: 0;
}

.message-option {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
}

.message-option input[type="radio"] {
    margin-right: 10px;
    margin-top: 4px;
    flex-shrink: 0;
}

.message-option label {
    flex: 1;
    cursor: pointer;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fafafa;
    transition: all 0.2s;
}

.message-option label:hover {
    background: #f0f0f0;
    border-color: var(--primary-color);
}

.message-option input[type="radio"]:checked + label {
    background: #e3f2fd;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 119, 181, 0.1);
}

.message-option .message-text {
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-color);
    margin: 0;
}

.profile-meta {
    padding: 10px 15px;
    background: #f9f9f9;
    border-top: 1px solid var(--border-color);
    font-size: 11px;
    color: #666;
    display: flex;
    justify-content: space-between;
}
